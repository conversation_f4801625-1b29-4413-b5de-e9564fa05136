export const handler = async (event, context, callback) => {
    const request = event.Records[0].cf.request;
    const querystring = request.querystring;

    console.log("Lambda@Edge Request:", {
        uri: request.uri,
        querystring,
        requestId: context.awsRequestId,
        distributionId: event.Records[0].cf.config.distributionId,
    });

    try {
        const token = extractToken(querystring);

        if (!token) {
            return callback(null, createErrorResponse(403, "Access token required"));
        }

        const isValid = await validateToken(token);
        if (!isValid) {
            return callback(null, createErrorResponse(403, "Invalid token"));
        }

        callback(null, request);
    } catch (error) {
        console.error("Error processing request:", error);
        callback(null, createErrorResponse(500, "Something went wrong"));
    }
};

async function validateToken(token) {
    try {
        const response = await fetch(
            `https://publish2.wonderslate.com/funlearn/validateToken?token=${token}`
        );
        if (!response.ok) {
            return false;
        }
        const data = await response.json();

        if(data.status == "success") {
            return true;
        } else {
            return false
        }
    } catch (error) {
        console.error("Error validating token:", "Invalid token or Not accessible");
        return false;
    }
}

function extractToken(querystring) {
    if (!querystring) return null;
    for (const part of querystring.split("&")) {
        const [key, val] = part.split("=");
        if (key === "token") return decodeURIComponent(val || "");
    }
    return null;
}

function removeTokenFromQueryString(querystring) {
    if (!querystring) return "";
    return querystring
        .split("&")
        .filter((p) => !p.startsWith("token="))
        .join("&");
}

function createErrorResponse(statusCode, message) {
    return {
        status: statusCode.toString(),
        statusDescription: message,
        headers: {
            "content-type": [{ key: "Content-Type", value: "text/plain" }],
            "cache-control": [{ key: "Cache-Control", value: "no-store, no-cache" }],
        },
        body: message,
    };
}
