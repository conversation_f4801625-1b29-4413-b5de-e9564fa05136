export const handler = async (event, context, callback) => {
    const request = event.Records[0].cf.request;
    const querystring = request.querystring;

    console.log(request.clientIp);

    console.log("Lambda@Edge Request:", {
        uri: request.uri,
        querystring,
        requestId: context.awsRequestId,
        distributionId: event.Records[0].cf.config.distributionId,
    });

    try {
        // Get the host header to check the domain
        const hostHeader = request.headers.host && request.headers.host[0] ? request.headers.host[0].value : '';
        console.log("Host header:", hostHeader);

        // Check if the request is for publish2.wonderslate.com
        const isPublish2Domain = hostHeader.includes('publish2.wonderslate.com');
        console.log("Is publish2 domain:", isPublish2Domain);

        // Only validate token if the request is for publish2.wonderslate.com
        if (isPublish2Domain) {
            const token = extractToken(querystring);
            console.log("Extracted token:", token);

            if (!token) {
                return callback(null, createErrorResponse(403, "Access token required"));
            }

            const isValid = await validateToken(token);
            if (!isValid) {
                return callback(null, createErrorResponse(403, "Invalid token"));
            }
        } else {
            console.log("Request is not for publish2.wonderslate.com, skipping token validation");
        }

        callback(null, request);
    } catch (error) {
        console.error("Error processing request:", error);
        callback(null, createErrorResponse(500, "Something went wrong"));
    }
};

async function validateToken(token) {
    try {
        const response = await fetch(
            `https://publish2.wonderslate.com/funlearn/validateToken?token=${token}`
        );
        if (!response.ok) {
            return false;
        }
        const data = await response.json();
        console.log("Response:", data);
        if (data.status == "success") {
            return true;
        } else {
            return false
        }
    } catch (error) {
        console.error("Error validating token:", "Invalid token or Not accessible");
        return false;
    }
}

function extractToken(querystring) {
    if (!querystring) return null;
    for (const part of querystring.split("&")) {
        const [key, val] = part.split("=");
        if (key === "token") return decodeURIComponent(val || "");
    }
    return null;
}

function removeTokenFromQueryString(querystring) {
    if (!querystring) return "";
    return querystring
        .split("&")
        .filter((p) => !p.startsWith("token="))
        .join("&");
}

function createErrorResponse(statusCode, message) {
    return {
        status: statusCode.toString(),
        statusDescription: message,
        headers: {
            "content-type": [{ key: "Content-Type", value: "text/plain" }],
            "cache-control": [{ key: "Cache-Control", value: "no-store, no-cache" }],
        },
        body: message,
    };
}
